import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  model,
  property,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
  HttpErrors,
  RestBindings,
} from '@loopback/rest';
import { Inspection, Action } from '../models';
import { InspectionRepository, ActionRepository, UserRepository } from '../repositories';
import moment from 'moment';
import { inject } from '@loopback/core';
import { SecurityBindings, securityId, UserProfile } from '@loopback/security';
import { authenticate } from '@loopback/authentication';
import { v4 as uuidv4 } from 'uuid';
import { LocationFilterService } from '../services';

@authenticate('jwt')
export class InspectionController {
  constructor(
    @repository(InspectionRepository)
    public inspectionRepository: InspectionRepository,
    @repository(ActionRepository)
    public actionRepository: ActionRepository,
    @repository(UserRepository)
    public userRepository: UserRepository,
    @inject('services.LocationFilterService')
    public locationFilterService: LocationFilterService,
  ) { }

  @post('/inspections')
  @response(200, {
    description: 'Inspection model instance',
    content: { 'application/json': { schema: getModelSchemaRef(Inspection) } },
  })
  async create(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, {
            title: 'NewInspection',
            exclude: ['id'],
          }),
        },
      },
    })
    inspection: Omit<Inspection, 'id'>,
  ): Promise<Inspection> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const count = await this.inspectionRepository.count();
    inspection.assignedById = user?.id ?? '';
    inspection.maskId = `INS-${moment().format('YYMMDD')}-${String(count.count + 1).padStart(4, '0')}`
    inspection.created = new Date().toISOString();
    const inspectionData = await this.inspectionRepository.create(inspection);
    let actionItem = {
      application: "Inspection",
      actionType: "inspect",

      description: `${uuidv4()}`,
      dueDate: inspection.dateTime,

      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: inspectionData.id,
      submittedById: user?.id,
      assignedToId: inspection.assignedToId
    }
    await this.actionRepository.create(actionItem)
    return inspectionData;
  }


  @get('/my-inspection-actions')
  @response(200, {
    description: 'Inspection model instance',
    content: { 'application/json': { schema: getModelSchemaRef(Inspection) } },
  })
  async getMyInspectionActions(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,

  ): Promise<any> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });


    const actions = await this.actionRepository.find({ where: { application: 'Inspection', submittedById: user?.id } })

    const inspectionIds = [...new Set(actions.map(i => i.objectId))];

    // Initialize an array to hold inspection data
    const inspectionsArray = [];

    // Loop through each unique ID to fetch the inspection data with included relations
    for (const id of inspectionIds) {
      const inspection = await this.inspectionRepository.findById(id, {
        include: [
          { relation: 'checklist' },
          { relation: 'assignedTo' },
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'actions' },
          { relation: 'approver' },
        ],
      });

      if (inspection) {
        inspectionsArray.push(inspection);
      }
    }

    // Return the array of inspection data with included relations
    return inspectionsArray;



  }

  @get('/download-inspection-pdf/{id}', {
    responses: {
      '200': {
        description: 'PDF download',
        content: { 'application/pdf': { schema: { type: 'string', format: 'binary' } } },
      },
    },
  })
  @authenticate.skip()
  async downloadPdf(
    @inject(RestBindings.Http.RESPONSE) response: Response,
    @param.path.string('id') id: string,
    @param.filter(Inspection, { exclude: 'where' }) filter?: FilterExcludingWhere<Inspection>

  ): Promise<void> {
    try {
      const extendedFilter = {
        include: [
          { relation: 'permitReportAction' },
          { relation: 'applicant' },
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'locationFive' },
          { relation: 'locationSix' },
        ],
      };
      const permit = await this.permitReportRepository.findById(id, extendedFilter);
      const permitChecklist = await this.eptwChecklistRepository.find();
      const pdfBuffer = await this.pdfService.generatePdf(permit, permitChecklist);
      response.setHeader('Content-Type', 'application/pdf');
      response.setHeader('Content-Disposition', `attachment; filename=${permit.maskId}.pdf`);
      response.end(pdfBuffer);
    } catch (error) {
      console.error('Error generating PDF:', error);
      response.status(500).send('Error generating PDF');
    }
  }

  @get('/all-active-inspections')
  @response(200, {
    description: 'Array of Report Incident model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Inspection, { includeRelations: true }),
        },
      },
    },
  })
  @authenticate('jwt')
  async findAllActiveByLocation(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Inspection) filter?: Filter<Inspection>,
  ): Promise<any> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId

      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
      const whereClause = {
        and: [
          {
            or: filterConditions.map(andConditions => ({
              and: andConditions,
            }))
          }
        ]
        ,
      };
      const inspectionReports = await this.inspectionRepository.find({
        ...filter,
        where: whereClause,
        include: [
          { relation: 'checklist' },
          { relation: 'assignedBy' },
          { relation: 'assignedTo' },
          { relation: 'locationOne' },
          { relation: 'locationTwo' },
          { relation: 'locationThree' },
          { relation: 'locationFour' },
          { relation: 'actions' },
          { relation: 'approver' },
        ]
      });

      const filteredInspectionReports = inspectionReports.filter(report => {
        const createdDate = moment(report.created);
        const status = report?.status?.toLowerCase();
        const thirtyDaysAgo = moment().subtract(30, 'days');

        // If status is 'completed', include only reports from the last 30 days
        if (status === 'completed') {
          return createdDate.isAfter(thirtyDaysAgo);
        }

        // For all other statuses, include all reports
        return true;
      });
      return filteredInspectionReports;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }

  @get('/inspections/count')
  @response(200, {
    description: 'Inspection model count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async count(
    @param.where(Inspection) where?: Where<Inspection>,
  ): Promise<Count> {
    return this.inspectionRepository.count(where);
  }

  @get('/inspections')
  @response(200, {
    description: 'Array of Inspection model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Inspection, { includeRelations: true }),
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.filter(Inspection) filter?: Filter<Inspection>,
  ): Promise<Inspection[]> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    if (user) {
      const userIdToSearch = user.id; // replace with the actual user ID

      // 1. Fetch all UserLocationRoles based on userId
      const filterConditions = await this.locationFilterService.getLocationFilterConditions(userIdToSearch);
      const whereClause = {
        or: filterConditions.map(andConditions => ({
          and: andConditions,
        })),
      };

      const inspections = await this.inspectionRepository.find({
        ...filter,
        where: whereClause
      });

      const modifiedInspection = await Promise.all(
        inspections.map(async (data) => {

          // Then fetch actions for each audit finding and include the auditFindings data in each action

          const totalActions = await this.actionRepository.find({ where: { objectId: data.id } });
          // Map each action to include the auditFinding data under applicationDetails


          // Filter actions to find those that are completed
          const completedActions = totalActions.filter(action => action.status === 'completed');

          // Create an instance of ReportIncident with the desired properties
          const modifiedReport = new Inspection({
            ...data,
            inspectionData: {

              totalActions: totalActions,
              completedActions: completedActions
            }
          });

          return modifiedReport;
        })
      );


      return modifiedInspection;

    } else {
      throw new HttpErrors.NotFound('user not found')
    }

  }

  @patch('/inspections')
  @response(200, {
    description: 'Inspection PATCH success count',
    content: { 'application/json': { schema: CountSchema } },
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, { partial: true }),
        },
      },
    })
    inspection: Inspection,
    @param.where(Inspection) where?: Where<Inspection>,
  ): Promise<Count> {
    return this.inspectionRepository.updateAll(inspection, where);
  }

  @get('/inspections/{id}')
  @response(200, {
    description: 'Inspection model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Inspection, { includeRelations: true }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Inspection, { exclude: 'where' }) filter?: FilterExcludingWhere<Inspection>
  ): Promise<Inspection> {
    return this.inspectionRepository.findById(id, filter);
  }

  @patch('/inspections/{id}/{action_id}')
  @response(204, {
    description: 'Inspection PATCH success',
  })
  async updateById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @param.path.string('action_id') action_id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, { partial: true }),
        },
      },
    })
    inspection: Inspection,
  ): Promise<void> {
    let status = '';
    let actionItem = [];
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const inspectionData = await this.inspectionRepository.findById(id)
    if (!inspectionData) {
      throw new Error('Inspection not found')
    }
    if (inspection.postActions && inspection.postActions.length > 0) {
      status = 'Done. (Open Actions)';
      actionItem = inspection.postActions.map((action: any, index: number) => {
        {
          return {
            application: "Inspection",
            actionType: "ins_take_actions_control",
            description: `${uuidv4()}`,
            actionToBeTaken: action.actionToBeTaken,
            dueDate: action.dueDate,
            maskId: inspectionData.maskId,
            sequenceNo: index + 1,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            uploads: action.uploads,
            assignedToId: action.actionOwner

          }
        }
      }
      )
      await this.actionRepository.createAll(actionItem)
    } else {
      status = 'Closed'
    }
    inspection.status = status;
    await this.inspectionRepository.updateById(id, inspection);

    await this.actionRepository.updateById(action_id, { status: 'completed' })
  }

  @patch('/inspections-action/{id}')
  @response(204, {
    description: 'Inspection PATCH success',
  })
  async updateActionsControlsById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, { partial: true }),
        },
      },
    })
    inspection: Inspection,
  ): Promise<void> {
    let status = '';
    let actionItem = [];
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const inspectionData = await this.inspectionRepository.findById(id);

    if (!inspectionData) {
      throw new Error('Inspection not found')
    }

    // Get existing actions to find the latest sequenceNo
    const existingActions = await this.actionRepository.find({
      where: {
        objectId: id,
        actionType: "ins_take_actions_control"
      },
      order: ['sequenceNo DESC'],
      limit: 1
    });

    // Get the latest sequenceNo or start from 0 if no actions exist
    const latestSequenceNo = existingActions.length > 0 ? existingActions[0].sequenceNo : -1;

    if (inspection.postActions && inspection.postActions.length > 0) {
      status = 'Done. (Open Actions)';
      actionItem = inspection.postActions.map((action: any, index: number) => {
        {
          return {
            application: "Inspection",
            actionType: "ins_take_actions_control",
            description: `${uuidv4()}`,
            actionToBeTaken: action.actionToBeTaken,
            dueDate: action.dueDate,
            maskId: inspectionData.maskId,
            sequenceNo: (latestSequenceNo ? Number(latestSequenceNo) : 0) + 1 + index,
            status: 'open',
            createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
            objectId: id,
            submittedById: user?.id,
            uploads: action.uploads,
            assignedToId: action.actionOwner
          }
        }
      });
      await this.actionRepository.createAll(actionItem)
    } else {
      status = 'Closed'
    }
    inspection.status = status;

    inspection.postActions = [...inspection.postActions, ...inspectionData.postActions];
    await this.inspectionRepository.updateById(id, inspection);


  }

  @patch('inspections/actions/{id}')
  @response(204, {
    description: 'Action PATCH success',
  })
  async updateActionsById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Action, { partial: true }),
        },
      },
    })
    action: Action,
  ): Promise<void> {
    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });

    action.status = 'submitted'
    if (action.actionType === 'ins_take_actions_control' || action.actionType === 'ins_retake_actions') {
      delete action.actionType



      const actionItem = {
        application: "Inspection",
        actionType: "ins_verify_actions",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        maskId: action.maskId,
        sequenceNo: action.sequenceNo,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.assignedToId
      }



      await this.actionRepository.create(actionItem)

    }

    if (action.actionType === 'ins_verify_actions') {

      if (action.objectId) {

        const observationDetails = await this.inspectionRepository.findById(action.objectId, {
          include: [
            { relation: 'locationOne' },
            { relation: 'locationTwo' },
            { relation: 'locationThree' },
            { relation: 'locationFour' },


          ]
        })



      }

    }

    if (action.actionType === 'reject') {

      const actionItem = {
        application: "Inspection",
        actionType: "ins_retake_actions",
        comments: action.comments,
        actionTaken: action.actionTaken,
        actionToBeTaken: action.actionToBeTaken,
        description: action.description,
        maskId: action.maskId,
        sequenceNo: action.sequenceNo,
        dueDate: action.dueDate,
        uploads: action.uploads,
        status: "open",
        createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
        objectId: action.objectId,
        submittedById: user?.id,
        assignedToId: action.assignedToId
      }

      await this.actionRepository.create(actionItem)
    }
    delete action.assignedToId
    await this.actionRepository.updateById(id, action);
  }

  @put('/inspections/{id}')
  @response(204, {
    description: 'Inspection PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() inspection: Inspection,
  ): Promise<void> {
    await this.inspectionRepository.replaceById(id, inspection);
  }

  @patch('/inspections/{id}')
  @response(204, {
    description: 'Inspection PATCH success',
  })
  async updateInspectionById(
    @inject(SecurityBindings.USER)
    currentUserProfile: UserProfile,
    @param.path.string('id') id: string,

    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Inspection, { partial: true }),
        },
      },
    })
    inspection: Inspection,
  ): Promise<void> {

    const email = currentUserProfile.email;
    const user = await this.userRepository.findOne({ where: { email: email } });
    const inspectionData = await this.inspectionRepository.findById(id);


    let actionItem = {
      application: "Inspection",
      actionType: "inspect",

      description: `${uuidv4()}`,
      dueDate: inspection.dateTime,

      status: "open",
      createdDate: moment(new Date()).format('DD-MM-YYYY HH:mm'),
      objectId: inspectionData.id,
      submittedById: user?.id,
      assignedToId: inspection.assignedToId
    }
    await this.actionRepository.deleteAll({ objectId: inspectionData.id })
    await this.actionRepository.create(actionItem)


    if (!inspectionData) {
      throw new Error('Inspection not found')
    }
    inspection.assignedById = user?.id ?? '';

    await this.inspectionRepository.updateById(id, inspection);


  }

  @del('/inspections/{id}')
  @response(204, {
    description: 'Inspection DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.inspectionRepository.deleteById(id);
    await this.actionRepository.deleteAll({ objectId: id })
  }
}


